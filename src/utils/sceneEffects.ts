/* Lightweight cinematic scene effects for chat page.
   - Pointer & device orientation parallax (applied to provided root element)
   - Click ripple and particle burst
   - Optional click sound via WebAudio (no assets)
   All effects are additive and self-cleaning via disposer.
*/

type WindowWithWebAudio = Window & {
  AudioContext?: typeof AudioContext;
  webkitAudioContext?: typeof AudioContext;
};

export function initSceneEffects(root: HTMLElement) {
  const disposers: Array<() => void> = [];

  // --- 1) Pointer parallax (update CSS vars on root) ---
  const handlePointer = (ev: PointerEvent) => {
    const rect = root.getBoundingClientRect();
    const cx = rect.left + rect.width / 2;
    const cy = rect.top + rect.height / 2;
    const dx = (ev.clientX - cx) / rect.width; // -0.5 ~ 0.5
    const dy = (ev.clientY - cy) / rect.height; // -0.5 ~ 0.5
    // Limit tilt (strong effect)
    const maxX = 3.0; // deg
    const maxY = 4.0; // deg
    root.style.setProperty('--pRY', `${-dx * maxY}deg`); // rotateY by horizontal
    root.style.setProperty('--pRX', `${dy * maxX}deg`); // rotateX by vertical
  };
  root.addEventListener('pointermove', handlePointer, { passive: true });
  disposers.push(() => root.removeEventListener('pointermove', handlePointer));

  // --- 2) Device orientation parallax (mobile) ---
  const handleOrientation = (e: DeviceOrientationEvent) => {
    const beta = e.beta ?? 0; // front-back tilt [-180,180]
    const gamma = e.gamma ?? 0; // left-right tilt [-90,90]
    const kx = 0.02; // sensitivity
    const ky = 0.03;
    root.style.setProperty('--pRX', `${beta * kx}deg`);
    root.style.setProperty('--pRY', `${gamma * ky}deg`);
  };
  window.addEventListener('deviceorientation', handleOrientation, true);
  disposers.push(() => window.removeEventListener('deviceorientation', handleOrientation, true));

  // --- 3) Click ripple & particle burst ---
  const createRipple = (x: number, y: number) => {
    const el = document.createElement('div');
    el.className = 'scene-ripple';
    el.style.left = `${x - 2}px`;
    el.style.top = `${y - 2}px`;
    root.appendChild(el);
    const remove = () => el.remove();
    el.addEventListener('animationend', remove, { once: true });

    // Energy wave concentric ring after 100ms
    setTimeout(() => {
      const ring = document.createElement('div');
      ring.className = 'scene-energy';
      ring.style.left = `${x}px`;
      ring.style.top = `${y}px`;
      root.appendChild(ring);
      ring.addEventListener('animationend', () => ring.remove(), { once: true });
    }, 100);
  };

  const createParticles = (x: number, y: number, n = 14) => {
    for (let i = 0; i < n; i++) {
      const p = document.createElement('div');
      p.className = 'scene-particle';
      const size = 4 + Math.random() * 4;
      p.style.width = `${size}px`;
      p.style.height = `${size}px`;
      p.style.left = `${x}px`;
      p.style.top = `${y}px`;
      const angle = Math.random() * Math.PI * 2;
      const speed = 40 + Math.random() * 90; // px
      const dx = Math.cos(angle) * speed;
      const dy = Math.sin(angle) * speed;
      const dur = 600 + Math.random() * 500; // ms
      p.style.setProperty('--tx', `${dx}px`);
      p.style.setProperty('--ty', `${dy}px`);
      p.style.setProperty('--dur', `${dur}ms`);
      p.style.opacity = String(0.6 + Math.random() * 0.4);
      root.appendChild(p);
      setTimeout(() => p.remove(), dur + 50);
    }
  };

  // --- 4) Optional click sound via WebAudio ---
  let audioCtx: AudioContext | null = null;
  const playClickSound = () => {
    try {
      const w = window as WindowWithWebAudio;
      const AudioContextCls = (w.AudioContext || w.webkitAudioContext) as typeof AudioContext | undefined;
      if (!AudioContextCls) return; // environment without WebAudio
      if (!audioCtx) audioCtx = new AudioContextCls();
      const ctx: AudioContext = audioCtx;
      const osc = ctx.createOscillator();
      const gain = ctx.createGain();
      osc.type = 'sine';
      osc.frequency.value = 740; // A5
      gain.gain.value = 0.0001; // start low to avoid click
      osc.connect(gain).connect(ctx.destination);
      const now = ctx.currentTime;
      osc.start(now);
      gain.gain.exponentialRampToValueAtTime(0.04, now + 0.01);
      osc.frequency.exponentialRampToValueAtTime(440, now + 0.08);
      gain.gain.exponentialRampToValueAtTime(0.00001, now + 0.16);
      osc.stop(now + 0.18);
    } catch (err) {
      // no-op: audio might be blocked by user gesture policy or unsupported
      // console.debug('Audio click sound skipped:', err);
    }
  };

  const handleClick = (ev: MouseEvent) => {
    const rect = root.getBoundingClientRect();
    const x = ev.clientX - rect.left;
    const y = ev.clientY - rect.top;
    createRipple(x, y);
    createParticles(x, y);
    playClickSound();
  };
  root.addEventListener('click', handleClick);
  disposers.push(() => root.removeEventListener('click', handleClick));

  // Cleanup and reset vars
  const dispose = () => {
    disposers.forEach((fn) => fn());
    root.style.removeProperty('--pRX');
    root.style.removeProperty('--pRY');
  };

  return dispose;
}
