/* Chat scene cinematic styling - non-scoped, additive only.
   Avoid name collisions and heavy DOM edits. Uses gradients instead of image assets. */

/* 1) Multi-layer background using pseudo-elements on the chat content wrapper */
.welcome-content.chat-content {
  position: relative;
  isolation: isolate; /* ensure z-index layering isolation */
  perspective: 900px;
  perspective-origin: 50% 40%;
  overflow: visible; /* allow ripples/particles to extend */
  /* subtle inset edge shading to create bevel/corner depth */
  box-shadow:
    inset 0 36px 60px rgba(0, 0, 0, 0.14),
    inset 0 -28px 44px rgba(0, 0, 0, 0.12),
    inset 24px 0 36px rgba(0, 0, 0, 0.1),
    inset -24px 0 36px rgba(0, 0, 0, 0.1);
}

/* Far starfield layer - LIGHT BEAMS DISABLED */
.welcome-content.chat-content::before {
  display: none;
}

/* Near nebula/god rays layer - LIGHT BEAMS DISABLED */
.welcome-content.chat-content::after {
  display: none;
}

@keyframes scene-panX {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-6%);
  }
}
@keyframes scene-zoom {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.04);
  }
}

/* moving specular highlight */
@keyframes scene-sheen {
  0% {
    background-position:
      center,
      center,
      top center,
      -60% -80%,
      center;
  }
  50% {
    background-position:
      center,
      center,
      top center,
      50% 40%,
      center;
  }
  100% {
    background-position:
      center,
      center,
      top center,
      140% 160%,
      center;
  }
}

/* 2) Camera sway applied to the grid layout (lightweight) */
@property --sRX {
  syntax: "<angle>";
  inherits: false;
  initial-value: 0deg;
}
@property --sRY {
  syntax: "<angle>";
  inherits: false;
  initial-value: 0deg;
}

.ai-assistant-grid-layout {
  /* Combine pointer/device parallax (pR*) with sway variables (sR*) */
  transform: rotateX(calc(var(--pRX, 0deg) + var(--sRX, 0deg)))
    rotateY(calc(var(--pRY, 0deg) + var(--sRY, 0deg)));
  transform-style: preserve-3d;
  animation: scene-swayVars 7.2s ease-in-out infinite;
  position: relative;
  z-index: 2; /* keep content above decorative floor */
}

@keyframes scene-swayVars {
  0%,
  100% {
    --sRX: 0deg;
    --sRY: 0deg;
  }
  50% {
    --sRX: 1.2deg;
    --sRY: 1.6deg;
  }
}

/* 2.1) Floor reflection under the grid (angled plane) */
.ai-assistant-grid-layout::before {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -18px;
  width: 140%;
  height: 70%;
  transform: translateX(-50%) rotateX(74deg);
  transform-origin: top center;
  background:
    radial-gradient(
      80% 60% at 50% 10%,
      rgba(0, 200, 255, 0.28),
      transparent 70%
    ),
    radial-gradient(
      60% 40% at 50% 0%,
      rgba(255, 255, 255, 0.12),
      transparent 55%
    );
  filter: blur(10px) saturate(1.05);
  pointer-events: none;
  mix-blend-mode: screen;
  opacity: 0; /* disabled: fold should be under main avatar, not between rows */
  z-index: 0;
}

/* 2.2) Isometric zebra crossing / guide stripes on the floor */
.ai-assistant-grid-layout::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -26px;
  width: 170%;
  height: 85%;
  transform: translateX(-50%) rotateX(78deg);
  transform-origin: top center;
  background:
    /* angled zebra stripes */ repeating-linear-gradient(
    100deg,
    rgba(255, 255, 255, 0.15) 0 14px,
    transparent 14px 30px
  );
  background-size: 100% 100%;
  background-position: 0 0;
  filter: blur(1px) saturate(1.1);
  opacity: 0; /* disabled: move zebra plane under main avatar */
  mix-blend-mode: screen;
  animation: scene-floorDrift 18s linear infinite;
  pointer-events: none;
  z-index: -1; /* sit under reflection */
}

@keyframes scene-floorDrift {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* Floor planes anchored to the main assistant avatar (fold right under Lao Dong) */
.welcome-content.chat-content .main-assistant-avatar {
  position: relative;
  transform-style: preserve-3d;
}

/* Reflection glow under main avatar */
.welcome-content.chat-content .main-assistant-avatar::before {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -22px;
  width: 210%;
  height: 120%;
  transform: translateX(-50%) rotateX(78deg);
  transform-origin: top center;
  /* Disabled: remove circular disc under the main avatar */
  background: none;
  opacity: 0;
  pointer-events: none;
  z-index: 0;
}

/* Isometric zebra plane under main avatar - DISABLED */
.welcome-content.chat-content .main-assistant-avatar::after {
  display: none;
}

/* 4.1) Ground Reflection Effect - Mirror image on floor */
.features-row .avatar-circle::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -24px;
  width: 100%;
  height: 60%;
  transform: translateX(-50%) rotateX(180deg) rotateY(0deg) scaleY(0.6);
  transform-origin: top center;

  /* Create reflection appearance */
  background: 
    /* Copy of avatar (will be masked) */
    var(--avatar-bg, #333),
    /* Reflection gradient overlay */
      linear-gradient(
        to bottom,
        transparent 0%,
        rgba(0, 0, 0, 0.3) 30%,
        rgba(0, 0, 0, 0.7) 70%,
        rgba(0, 0, 0, 0.9) 100%
      );

  background-size:
    cover,
    100% 100%;
  background-blend-mode: multiply;
  border-radius: inherit;
  opacity: 0.4;
  filter: blur(1px);
  pointer-events: none;
  z-index: -2;
  transition: all 0.3s ease;

  /* Reflection mask for realistic fade */
  -webkit-mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
}

/* Enhanced reflections per row */
.features-row.available-features .avatar-circle::after {
  bottom: -32px;
  height: 70%;
  opacity: 0.5;
  transform: translateX(-50%) rotateX(180deg) scaleY(0.7);
  filter: blur(1.5px);
}

.features-row.future-features .avatar-circle::after {
  bottom: -28px;
  height: 65%;
  opacity: 0.45;
  transform: translateX(-50%) rotateX(180deg) scaleY(0.65);
  filter: blur(1.2px);
}

/* Top-features reflection - highest priority */
.welcome-content.chat-content .features-row.top-features .avatar-circle::after,
.welcome-content.chat-content
  .features-row.top-features
  .avatar-circle.disabled::after,
.features-row.top-features .avatar-circle::after,
.features-row.top-features .avatar-circle.disabled::after {
  bottom: -28px !important;
  height: 65% !important;
  opacity: 0.7 !important;
  transform: translateX(-50%) rotateX(180deg) scaleY(0.65) !important;
  filter: blur(1.8px) brightness(0.75) !important;
  background:
    radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.35) 0%,
      rgba(0, 255, 255, 0.25) 30%,
      rgba(0, 120, 180, 0.18) 60%,
      transparent 80%
    ),
    linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.12) 0%,
      rgba(0, 255, 255, 0.1) 20%,
      rgba(0, 0, 0, 0.45) 60%,
      rgba(0, 0, 0, 0.85) 100%
    ) !important;
}

/* Main avatar gets the strongest reflection */
.welcome-content.chat-content .main-assistant-avatar .avatar-circle::after {
  bottom: -40px;
  height: 80%;
  opacity: 0.6;
  transform: translateX(-50%) rotateX(180deg) scaleY(0.8);
  filter: blur(2px);
}

/* Reflection responds to hover */
.features-row .avatar-circle:hover::after {
  opacity: 0.6;
  transform: translateX(-50%) rotateX(180deg) scaleY(0.7) scale(1.05);
}

/* 3) Depth haze and brightness per row (soft game-like stageing) */
.features-row.top-features {
  filter: saturate(0.98);
}
.features-row.future-features {
  filter: saturate(0.98);
}
.features-row.available-features {
  filter: saturate(1.05);
}

/* 4) Standing Card Avatar Effects - Like upright 3D cards with base */
.features-row .avatar-circle {
  position: relative;
  transform-style: preserve-3d;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  /* Standing card appearance - tilted back slightly */
  transform: translateZ(0px) rotateX(-8deg) translateY(-12px);

  /* Card-like shadow system */
  box-shadow: 
    /* Main card shadow on ground */
    0 24px 48px rgba(0, 0, 0, 0.6),
    0 12px 24px rgba(0, 0, 0, 0.4),
    /* Card edge glow */ 0 0 20px rgba(0, 255, 255, 0.3),
    /* Inner card lighting */ inset 0 4px 12px rgba(255, 255, 255, 0.3),
    inset 0 -2px 8px rgba(0, 0, 0, 0.2),
    /* Card border highlight */ inset 0 0 0 2px rgba(255, 255, 255, 0.15);

  /* Premium card border */
  border: 3px solid transparent;
  background:
    var(--avatar-bg, #333),
    linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.5) 0%,
      rgba(255, 255, 255, 0.2) 25%,
      rgba(0, 0, 0, 0.1) 50%,
      rgba(255, 255, 255, 0.2) 75%,
      rgba(255, 255, 255, 0.4) 100%
    );
  background-clip: padding-box, border-box;
}

/* Add card base/pedestal effect using ::before */
.features-row .avatar-circle::before {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -8px;
  width: 110%;
  height: 16px;
  transform: translateX(-50%) rotateX(85deg);
  transform-origin: top center;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2),
    rgba(0, 255, 255, 0.15) 50%,
    rgba(0, 0, 0, 0.3)
  );
  border-radius: 50%;
  box-shadow:
    0 0 20px rgba(0, 255, 255, 0.2),
    inset 0 2px 8px rgba(255, 255, 255, 0.3);
  pointer-events: none;
  z-index: -1;
}

/* Standing Card Effects - Different heights per row */
.features-row.available-features .avatar-circle {
  /* Closest row - tallest standing cards */
  transform: translateZ(0px) rotateX(-12deg) translateY(-20px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.7),
    0 16px 32px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(0, 255, 255, 0.5),
    inset 0 6px 16px rgba(255, 255, 255, 0.4),
    inset 0 -3px 12px rgba(0, 0, 0, 0.3),
    inset 0 0 0 3px rgba(255, 255, 255, 0.2);
  animation: scene-standingCardPulse 4.8s ease-in-out infinite;
}

.features-row.future-features .avatar-circle {
  /* Middle row - medium height cards */
  transform: translateZ(0px) rotateX(-10deg) translateY(-16px);
  box-shadow:
    0 28px 56px rgba(0, 0, 0, 0.6),
    0 14px 28px rgba(0, 0, 0, 0.45),
    0 0 32px rgba(0, 255, 255, 0.4),
    inset 0 5px 14px rgba(255, 255, 255, 0.35),
    inset 0 -2px 10px rgba(0, 0, 0, 0.25),
    inset 0 0 0 2px rgba(255, 255, 255, 0.15);
}

/* 最高优先级确保top-features的3D效果 */
.welcome-content.chat-content .features-row.top-features .avatar-circle,
.welcome-content.chat-content
  .features-row.top-features
  .avatar-circle.disabled,
.features-row.top-features .avatar-circle,
.features-row.top-features .avatar-circle.disabled {
  /* Same as future-features - medium height cards */
  transform: translateZ(0px) rotateX(-10deg) translateY(-16px) !important;
  box-shadow:
    0 28px 56px rgba(0, 0, 0, 0.6),
    0 14px 28px rgba(0, 0, 0, 0.45),
    0 0 32px rgba(0, 255, 255, 0.4),
    inset 0 5px 14px rgba(255, 255, 255, 0.35),
    inset 0 -2px 10px rgba(0, 0, 0, 0.25),
    inset 0 0 0 2px rgba(255, 255, 255, 0.15) !important;
}

/* Enhanced card base for different rows */
.features-row.available-features .avatar-circle::before {
  bottom: -12px;
  width: 120%;
  height: 20px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3),
    rgba(0, 255, 255, 0.2) 50%,
    rgba(0, 0, 0, 0.4)
  );
  box-shadow:
    0 0 30px rgba(0, 255, 255, 0.3),
    inset 0 3px 12px rgba(255, 255, 255, 0.4);
}

.features-row.future-features .avatar-circle::before {
  bottom: -10px;
  width: 115%;
  height: 18px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25),
    rgba(0, 255, 255, 0.18) 50%,
    rgba(0, 0, 0, 0.35)
  );
  box-shadow:
    0 0 25px rgba(0, 255, 255, 0.25),
    inset 0 2px 10px rgba(255, 255, 255, 0.35);
}

/* Top-features card base - highest priority */
.welcome-content.chat-content .features-row.top-features .avatar-circle::before,
.welcome-content.chat-content
  .features-row.top-features
  .avatar-circle.disabled::before,
.features-row.top-features .avatar-circle::before,
.features-row.top-features .avatar-circle.disabled::before {
  bottom: -10px !important;
  width: 115% !important;
  height: 18px !important;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25),
    rgba(0, 255, 255, 0.18) 50%,
    rgba(0, 0, 0, 0.35)
  ) !important;
  box-shadow:
    0 0 25px rgba(0, 255, 255, 0.25),
    inset 0 2px 10px rgba(255, 255, 255, 0.35) !important;
}
/* Standing Card Pulse Animation - Gentle sway */
@keyframes scene-standingCardPulse {
  0%,
  100% {
    transform: translateZ(0px) rotateX(-12deg) translateY(-20px) rotateY(0deg);
    box-shadow:
      0 32px 64px rgba(0, 0, 0, 0.7),
      0 16px 32px rgba(0, 0, 0, 0.5),
      0 0 35px rgba(0, 255, 255, 0.45),
      inset 0 6px 16px rgba(255, 255, 255, 0.4),
      inset 0 -3px 12px rgba(0, 0, 0, 0.3),
      inset 0 0 0 3px rgba(255, 255, 255, 0.2);
  }
  50% {
    transform: translateZ(0px) rotateX(-14deg) translateY(-22px) rotateY(1deg);
    box-shadow:
      0 36px 72px rgba(0, 0, 0, 0.8),
      0 18px 36px rgba(0, 0, 0, 0.6),
      0 0 50px rgba(0, 255, 255, 0.6),
      inset 0 8px 20px rgba(255, 255, 255, 0.5),
      inset 0 -4px 16px rgba(0, 0, 0, 0.4),
      inset 0 0 0 4px rgba(255, 255, 255, 0.25);
  }
}

/* 5) Standing Card Hover Effects - Cards lean forward */
.features-row .avatar-circle:hover {
  transform: translateZ(8px) rotateX(-15deg) translateY(-24px) rotateY(3deg)
    scale(1.05);
  box-shadow:
    0 40px 80px rgba(0, 0, 0, 0.8),
    0 20px 40px rgba(0, 0, 0, 0.6),
    0 0 60px rgba(0, 255, 255, 0.7),
    inset 0 8px 24px rgba(255, 255, 255, 0.6),
    inset 0 -4px 20px rgba(0, 0, 0, 0.4),
    inset 0 0 0 4px rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Card base glows more on hover */
.features-row .avatar-circle:hover::before {
  box-shadow:
    0 0 40px rgba(0, 255, 255, 0.5),
    inset 0 4px 16px rgba(255, 255, 255, 0.5);
  transform: translateX(-50%) rotateX(85deg) scale(1.1);
}

.feature-item:hover {
  transform: translateZ(16px) scale(1.02);
}

/* 6) Enhanced Main Assistant Avatar - Premium 3D Treatment */
.welcome-content.chat-content .main-assistant-avatar {
  position: relative;
  transform-style: preserve-3d;
}

/* Main avatar - Premium Standing Card */
.welcome-content.chat-content .main-assistant-avatar .avatar-circle {
  /* Tallest standing card with premium effects */
  transform: translateZ(0px) rotateX(-15deg) translateY(-32px);

  box-shadow: 
    /* Massive ground shadow */
    0 48px 96px rgba(0, 0, 0, 0.8),
    0 24px 48px rgba(0, 0, 0, 0.7),
    0 12px 24px rgba(0, 0, 0, 0.6),
    /* Intense premium glow */ 0 0 100px rgba(0, 255, 255, 0.7),
    0 0 60px rgba(0, 255, 255, 0.5),
    /* Premium inner lighting */ inset 0 8px 32px rgba(255, 255, 255, 0.6),
    inset 0 -6px 24px rgba(0, 0, 0, 0.4),
    inset 0 0 0 5px rgba(255, 255, 255, 0.25);

  animation: scene-mainCardFloat 6s ease-in-out infinite;

  /* Premium metallic border */
  border: 5px solid transparent;
  background:
    var(--avatar-bg, #333),
    linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.7) 0%,
      rgba(255, 255, 255, 0.3) 25%,
      rgba(0, 0, 0, 0.2) 50%,
      rgba(255, 255, 255, 0.3) 75%,
      rgba(255, 255, 255, 0.6) 100%
    );
  background-clip: padding-box, border-box;
}

/* Premium card base for main avatar */
.welcome-content.chat-content .main-assistant-avatar .avatar-circle::before {
  bottom: -16px;
  width: 130%;
  height: 24px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4),
    rgba(0, 255, 255, 0.3) 50%,
    rgba(0, 0, 0, 0.5)
  );
  box-shadow:
    0 0 50px rgba(0, 255, 255, 0.4),
    inset 0 4px 16px rgba(255, 255, 255, 0.5);
}

/* Main Card Floating Animation - Gentle sway */
@keyframes scene-mainCardFloat {
  0%,
  100% {
    transform: translateZ(0px) rotateX(-15deg) translateY(-32px) rotateY(0deg);
  }
  50% {
    transform: translateZ(4px) rotateX(-17deg) translateY(-36px) rotateY(1deg);
  }
}

/* Rotating rune ring - enhanced */
.welcome-content.chat-content .main-assistant-avatar::before {
  content: "";
  position: absolute;
  left: 50%;
  bottom: 120px;
  transform: translateX(-50%);
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    rgba(0, 255, 255, 0.4),
    rgba(255, 255, 255, 0.2) 30%,
    transparent 40%,
    rgba(0, 255, 255, 0.3) 70%
  );
  -webkit-mask: radial-gradient(
    circle at center,
    transparent 56%,
    black 58%,
    black 60%,
    transparent 62%
  );
  mask: radial-gradient(
    circle at center,
    transparent 56%,
    black 58%,
    black 60%,
    transparent 62%
  );
  filter: blur(1px);
  animation: scene-runeSpin 12s linear infinite;
  pointer-events: none;
  box-shadow: 0 0 40px rgba(0, 255, 255, 0.3);
}
@keyframes scene-runeSpin {
  to {
    transform: translateX(-50%) rotate(360deg);
  }
}

/* 8) Click ripple and particle burst */
.scene-ripple,
.scene-particle {
  position: absolute;
  pointer-events: none;
  z-index: 2; /* above content but under dialogs if any */
}
.scene-ripple {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(0, 255, 255, 0.6),
    rgba(0, 255, 255, 0.25) 40%,
    transparent 60%
  );
  box-shadow: 0 0 12px rgba(0, 255, 255, 0.5);
  animation: scene-ripple 600ms ease-out forwards;
}
@keyframes scene-ripple {
  from {
    transform: translate(-50%, -50%) scale(0.6);
    opacity: 0.9;
  }
  to {
    transform: translate(-50%, -50%) scale(14);
    opacity: 0;
  }
}
.scene-particle {
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.9),
    rgba(0, 255, 255, 0.6)
  );
  transform: translate(-50%, -50%);
  animation: scene-particle var(--dur, 900ms) ease-out forwards;
}
@keyframes scene-particle {
  to {
    transform: translate(
        calc(-50% + var(--tx, 0px)),
        calc(-50% + var(--ty, 0px))
      )
      scale(0.4);
    opacity: 0;
    filter: blur(1px);
  }
}

/* 7) Reduced motion & mobile tweaks */
/* Main avatar entrance (translateZ + scale, 600ms) */
.scene-main-enter .main-avatar {
  will-change: transform, opacity, filter;
  animation: scene-mainEnter 600ms cubic-bezier(0.2, 0.8, 0.2, 1) both;
}
@keyframes scene-mainEnter {
  from {
    transform: translateZ(-80px) scale(0.82);
    filter: blur(0.5px);
    opacity: 0;
  }
  to {
    transform: translateZ(100px) scale(1);
    filter: none;
    opacity: 1;
  }
}

/* Energy wave: thin concentric ring expanding after 100ms delay (trigger端延迟控制) */
.scene-energy {
  position: absolute;
  left: 0;
  top: 0;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  border: 1px solid rgba(0, 255, 255, 0.55);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.35);
  transform: translate(-50%, -50%) scale(1);
  animation: scene-energy 700ms ease-out forwards;
  pointer-events: none;
  z-index: 30;
}
@keyframes scene-energy {
  0% {
    opacity: 0.9;
  }
  80% {
    opacity: 0.35;
  }
  100% {
    transform: translate(-50%, -50%) scale(16);
    opacity: 0;
  }
}

/* 7) Reduced motion & mobile tweaks */
@media (prefers-reduced-motion: reduce) {
  .ai-assistant-grid-layout {
    animation: none;
  }
  .welcome-content.chat-content::before,
  .welcome-content.chat-content::after {
    animation: none;
  }
}

@media (max-width: 768px) {
  .welcome-content.chat-content::after {
    filter: blur(8px);
    opacity: 0.9;
  }
  .feature-item:hover {
    transform: none;
  }
}

/* 终极覆盖规则 - 确保top-features图标不会变扁 */
.v-chat-container .features-row.top-features .avatar-circle,
.v-chat-container .features-row.top-features .avatar-circle.disabled,
.v-chat-container .features-row.top-features .feature-item:nth-child(1) .avatar-circle,
.v-chat-container .features-row.top-features .feature-item:nth-child(2) .avatar-circle,
.v-chat-container .features-row.top-features .feature-item:nth-child(3) .avatar-circle,
.v-chat-container .features-row.top-features .feature-item:nth-child(4) .avatar-circle,
.v-chat-container .features-row.top-features .feature-item:nth-child(5) .avatar-circle {
  /* 强制使用与future-features相同的3D立体效果 */
  transform: translateZ(0px) rotateX(-10deg) translateY(-16px) !important;
  box-shadow: 
    0 28px 56px rgba(0, 0, 0, 0.6),
    0 14px 28px rgba(0, 0, 0, 0.45),
    0 0 32px rgba(0, 255, 255, 0.4),
    inset 0 5px 14px rgba(255, 255, 255, 0.35),
    inset 0 -2px 10px rgba(0, 0, 0, 0.25),
    inset 0 0 0 2px rgba(255, 255, 255, 0.15) !important;
}